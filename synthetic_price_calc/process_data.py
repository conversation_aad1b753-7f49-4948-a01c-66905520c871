import functools
import logging
import time
from collections import defaultdict
from datetime import UTC, datetime
from itertools import chain
from multiprocessing.connection import Connection
from typing import Any, cast

import orjson
import pandas as pd
import utils_calc
import utils_general
from utils_calc import Model, ModelParamsBase

from .config import NUM_WORKERS
from .synthetic_calc import (
    calc_synthetic_params,
    refit_svi_to_reduce_calendar_arb,
)
from .typings import (
    DataSetSnap,
    FailedTenorObject,
    ProcessDataResult,
    S3Details,
    SingleTenor,
    SingleTenorResult,
    Snapshot,
    SpotFromManager,
)
from .utils import extract_future_details, put_s3_object

# default='warn'
pd.options.mode.chained_assignment = None  # type: ignore


def process_chunk_helper(
    chunk: list[list[Snapshot]],
    freq: str,
    version: str,
    s3_details: S3Details,
    debug: bool = False,
    **kwargs: Any,  # Added for compatibility with blockstream utils
) -> list[ProcessDataResult]:

    version_prefix = ""

    if version:
        version_prefix = f"{version}."

    flattened_chunk: list[Snapshot] = list(chain.from_iterable(chunk))
    if not flattened_chunk:
        raise ValueError("Chunk is empty!")

    results = []
    failed_snapshots = []
    for snap in flattened_chunk:
        model_results: dict[str, list[SingleTenorResult]] = defaultdict(list)
        failed_model_results: dict[str, list[FailedTenorObject]] = defaultdict(
            list
        )

        try:
            for data in snap.data_snap:
                tenor_result, failed_tenor_info = _process_single_tenor(
                    data=data,
                    target_asset_spot_data=snap.target_asset_spot_data,
                    reference_asset_spot_data=snap.reference_asset_spot_data,
                    target_asset=snap.target_asset,
                    version_prefix=version_prefix,
                    timestamp=snap.timestamp,
                    debug=debug,
                )

                if failed_tenor_info is not None:
                    failed_model_results.setdefault(data.model, []).append(
                        failed_tenor_info
                    )

                if tenor_result is not None:
                    model_results.setdefault(data.model, []).append(
                        tenor_result
                    )

            # Note: SVI refitting moved to post-smoothing step in main pipeline

            for model, failed_tenors in failed_model_results.items():
                failed_snapshot_record = {
                    "model": model,
                    "failed_params": orjson.dumps(
                        [res["failed_tenor"] for res in failed_tenors],
                        option=orjson.OPT_SERIALIZE_NUMPY,
                    ).decode("utf-8"),
                    "reference_params": orjson.dumps(
                        [res["reference_tenor"] for res in failed_tenors],
                        option=orjson.OPT_SERIALIZE_NUMPY,
                    ).decode("utf-8"),
                    "timestamp": snap.timestamp,
                    "target_asset": snap.target_asset,
                    "reference_asset": snap.reference_asset,
                    "ref_asset_spot": snap.reference_asset_spot_data,
                    "target_asset_spot": snap.target_asset_spot_data,
                }
                failed_snapshots.append(failed_snapshot_record)

            for model, model_params in model_results.items():
                if model_params:
                    results.append(
                        ProcessDataResult(
                            timestamp=snap.timestamp,
                            params=orjson.dumps(
                                model_params, option=orjson.OPT_SERIALIZE_NUMPY
                            ).decode("utf-8"),
                            qualified_name=f"{version_prefix}blockscholes-syn.option.{snap.target_asset}.{model}.{freq}.params",
                            runtime=utils_general.to_iso(datetime.now(tz=UTC)),
                        )
                    )
                else:
                    logging.error(
                        f"Empty results for {model=}, iso_stamp={utils_general.to_iso(snap.timestamp)}, {snap.timestamp=}, {snap.target_asset=}, {snap.reference_asset=}, {snap.reference_exchange=} "
                    )

        except Exception:
            logging.exception(
                f"Error while processing snapshot iso_stamp={utils_general.to_iso(snap.timestamp)}, {snap.timestamp=}, {snap.target_asset=}, {snap.reference_asset=}, {snap.reference_exchange=}"
            )

    if failed_snapshots:
        try:
            put_s3_object(
                details=s3_details,
                data=pd.DataFrame(failed_snapshots).to_csv(index=False),
                sub_prefix="failed_snapshots",
            )
            logging.info("Stored failed snapshots to S3")
        except Exception:
            logging.exception("Failed to upload invalid snapshots to S3")

    return results


def _process_chunk(
    chunk: list[list[Snapshot]],
    conn: Connection,
    freq: str,
    version: str,
    s3_details: S3Details,
    debug: bool = False,
) -> None:
    try:
        results = process_chunk_helper(
            chunk=chunk,
            freq=freq,
            version=version,
            debug=debug,
            s3_details=s3_details,
        )
        conn.send(results)
    except Exception as e:
        conn.send({"error": str(e), "details": ""})
        logging.exception("Error processing data chunk")
    conn.close()


def preprocess_data_for_smoothing(
    processed_results: list[ProcessDataResult],
) -> dict[str, list[ProcessDataResult]]:
    """
    Preprocess data for smoothing by grouping results by qualified_name.

    Args:
        processed_results: Results from the initial data processing step

    Returns:
        Dictionary with qualified_name as key and list of ProcessDataResult as value
    """
    grouped_data: dict[str, list[ProcessDataResult]] = defaultdict(list)

    for result in processed_results:
        qualified_name = result["qualified_name"]
        grouped_data[qualified_name].append(result)

    return dict(grouped_data)


def smooth_chunk_helper(
    qualified_name_group: tuple[str, list[ProcessDataResult]],
    **kwargs: Any,  # Added for compatibility with blockstream utils
) -> list[ProcessDataResult]:
    """
    Apply smoothing operations to a group of data with the same qualified_name.

    Args:
        qualified_name_group: Tuple of (qualified_name, list of ProcessDataResult)

    Returns:
        List of smoothed ProcessDataResult objects
    """
    qualified_name, results = qualified_name_group

    if not results:
        return []

    # TODO: Implement actual smoothing algorithm
    # For now, return the original results unchanged
    logging.info(f"Smoothing {len(results)} results for {qualified_name}")

    # Placeholder: Apply smoothing logic here
    # This is where the actual smoothing algorithm will be implemented
    smoothed_results = results.copy()

    return smoothed_results


def _smooth_chunk(
    chunk: list[tuple[str, list[ProcessDataResult]]],
    conn: Connection,
) -> None:
    """
    Multiprocessing wrapper for smoothing operations.

    Args:
        chunk: List of (qualified_name, ProcessDataResult list) tuples
        conn: Multiprocessing connection for sending results
    """
    try:
        smoothed_results = []
        for qualified_name_group in chunk:
            group_results = smooth_chunk_helper(qualified_name_group)
            smoothed_results.extend(group_results)

        conn.send(smoothed_results)
    except Exception as e:
        conn.send({"error": str(e), "details": ""})
        logging.exception("Error smoothing data chunk")
    conn.close()


def process_data(
    data: list[list[Snapshot]],
    freq: str,
    version: str,
    s3_details: S3Details,
    debug: bool = False,
) -> list[ProcessDataResult]:

    t = time.time()

    # Step 1: Data processing (existing functionality)
    logging.info("Step 1: Processing data...")
    processed_results: list[ProcessDataResult] = utils_general.parallel_process(
        data_slices=data,
        num_workers=NUM_WORKERS,
        process_chunk_fn=functools.partial(
            _process_chunk,
            freq=freq,
            version=version,
            s3_details=s3_details,
            debug=debug,
        ),
        chunk_data=True,
    )

    # Step 2: Data preprocessing for smoothing
    logging.info("Step 2: Preprocessing data for smoothing...")
    grouped_data = preprocess_data_for_smoothing(processed_results)

    # Step 3: Smoothing operations (multiprocessing)
    logging.info("Step 3: Applying smoothing operations...")
    grouped_data_items = list(grouped_data.items())

    if grouped_data_items:
        smoothed_results: list[ProcessDataResult] = utils_general.parallel_process(
            data_slices=grouped_data_items,
            num_workers=NUM_WORKERS,
            process_chunk_fn=_smooth_chunk,
            chunk_data=True,
        )
    else:
        smoothed_results = []

    # Step 4: SVI refitting with smoothed data
    logging.info("Step 4: Applying SVI refitting to smoothed data...")
    if smoothed_results:
        # Group smoothed results by model and timestamp for SVI refitting
        smoothed_results = _apply_svi_refitting_to_smoothed_data(smoothed_results)

    # Step 5: Final result processing
    try:
        if smoothed_results:
            smoothed_results = sorted(smoothed_results, key=lambda x: x["timestamp"])
    except Exception:
        logging.exception("Error processing smoothed data")

    logging.info(f"SyntheticPrice calc took {round(time.time() - t)}s")
    return smoothed_results


def _group_smoothed_data_for_svi_refitting(
    smoothed_results: list[ProcessDataResult],
) -> dict[tuple[int, str], list[ProcessDataResult]]:
    """
    Group smoothed data by timestamp and model for SVI refitting.

    Args:
        smoothed_results: List of smoothed ProcessDataResult objects

    Returns:
        Dictionary grouped by (timestamp, model) tuples
    """
    grouped_for_svi: dict[tuple[int, str], list[ProcessDataResult]] = defaultdict(list)

    for result in smoothed_results:
        # Extract model from qualified_name (format: prefix.blockscholes-syn.option.asset.model.freq.params)
        qn_parts = result["qualified_name"].split(".")
        if len(qn_parts) >= 5:
            model = qn_parts[4]  # Extract model part
            timestamp = result["timestamp"]
            grouped_for_svi[(timestamp, model)].append(result)

    return dict(grouped_for_svi)


def _convert_process_data_results_to_model_results(
    results_group: list[ProcessDataResult],
) -> dict[str, list[SingleTenorResult]]:
    """
    Convert ProcessDataResult objects back to model_results format for SVI refitting.

    Args:
        results_group: List of ProcessDataResult objects for the same model/timestamp

    Returns:
        Dictionary in the format expected by refit_svi_to_reduce_calendar_arb
    """
    model_results: dict[str, list[SingleTenorResult]] = {"SVI": []}

    for result in results_group:
        try:
            # Parse the JSON params back to SingleTenorResult
            params_data = orjson.loads(result["params"])
            if isinstance(params_data, list) and params_data:
                model_results["SVI"].extend(params_data)
        except Exception:
            logging.exception(f"Error parsing params for SVI refitting: {result['qualified_name']}")
            continue

    return model_results


def _convert_model_results_back_to_process_data_results(
    model_results: dict[str, list[SingleTenorResult]],
    original_results_group: list[ProcessDataResult],
) -> list[ProcessDataResult]:
    """
    Convert refitted model_results back to ProcessDataResult format.

    Args:
        model_results: Refitted model results from SVI refitting
        original_results_group: Original ProcessDataResult objects to update

    Returns:
        List of ProcessDataResult objects with updated parameters
    """
    updated_results = []

    for i, result in enumerate(original_results_group):
        if i < len(model_results["SVI"]):
            # Update the result with refitted parameters
            result["params"] = orjson.dumps(
                [model_results["SVI"][i]], option=orjson.OPT_SERIALIZE_NUMPY
            ).decode("utf-8")
        updated_results.append(result)

    return updated_results


def _apply_svi_refitting_to_smoothed_data(
    smoothed_results: list[ProcessDataResult],
) -> list[ProcessDataResult]:
    """
    Apply SVI refitting to smoothed data, preserving the ProcessDataResult format.

    Args:
        smoothed_results: List of smoothed ProcessDataResult objects

    Returns:
        List of ProcessDataResult objects with SVI refitting applied
    """
    # Group results by timestamp and model for SVI refitting
    grouped_for_svi = _group_smoothed_data_for_svi_refitting(smoothed_results)

    refitted_results = []

    for (timestamp, model), results_group in grouped_for_svi.items():
        if model == "SVI" and len(results_group) > 1:
            # Convert to model_results format
            model_results = _convert_process_data_results_to_model_results(results_group)

            if model_results["SVI"]:
                # Apply SVI refitting
                try:
                    refit_svi_to_reduce_calendar_arb(model_results)

                    # Convert back to ProcessDataResult format
                    updated_results = _convert_model_results_back_to_process_data_results(
                        model_results, results_group
                    )
                    refitted_results.extend(updated_results)
                except Exception:
                    logging.exception(f"Error during SVI refitting for timestamp {timestamp}")
                    # Fall back to original results if refitting fails
                    refitted_results.extend(results_group)
            else:
                refitted_results.extend(results_group)
        else:
            # Non-SVI models or single results don't need refitting
            refitted_results.extend(results_group)

    return refitted_results


def _process_single_tenor(
    data: DataSetSnap,
    target_asset: str,
    target_asset_spot_data: list[SpotFromManager],
    reference_asset_spot_data: list[SpotFromManager],
    version_prefix: str,
    timestamp: int,
    debug: bool = False,
) -> tuple[SingleTenorResult | None, FailedTenorObject | None]:

    params = data.params
    future = data.future

    qn_freq, expiry_str = extract_future_details(future["qualified_name"])
    try:
        tenor_result, is_successful = calc_synthetic_params(
            ref_asset_daily_spot_pxs=reference_asset_spot_data,
            target_asset_daily_spot_pxs=target_asset_spot_data,
            tenor_days=params["expiry"] * 365,
            ref_params=params,
            model=data.model,
        )
        if not is_successful:
            if debug:
                return None, {
                    "failed_tenor": tenor_result,
                    "reference_tenor": params,
                }
            return None, None

        return (
            cast(
                SingleTenorResult,
                {
                    **tenor_result,
                    "qualified_name": f"{version_prefix}blockscholes-syn.option.{target_asset}.{data.model}.{expiry_str}.{qn_freq}.params",
                    "underlying_index": utils_general.get_qfn_and_version(
                        future["qualified_name"]
                    )[1][-3],
                    "spot": data.spot,
                    "forward": future["px"],
                    "atm_vol": _get_atm_vol(
                        future_px=future["px"],
                        expiry=params["expiry"],
                        model=cast(Model, data.model),
                        params=tenor_result,
                    ),
                },
            ),
            None,
        )

    except Exception:
        logging.exception(
            f"Error while creating synthetic params from {target_asset=} from {params['base']}. iso_stamp={utils_general.to_iso(timestamp)}, {timestamp=}, {expiry_str=}"
        )
        return None, None


def _get_atm_vol(
    future_px: float, expiry: float, model: Model, params: SingleTenor
) -> float:
    return float(
        utils_calc.utils_calc_helpers.model_vol(
            strike=(future_px),
            forward=(future_px),
            exp=expiry,
            model=model,
            model_params=cast(
                ModelParamsBase,
                utils_calc.extract_model_params(params, model),  # type: ignore
            ),
        ),
    )
